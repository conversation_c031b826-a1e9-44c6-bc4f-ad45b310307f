package com.yooa.crm.api.domain.vo;

import lombok.Data;
import com.yooa.common.core.annotation.Excel;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推广 - 充值记录 - 导出
 */
@Data
public class RechargeRecordExtendExportVo {
    /**
     * 平台 1=pd,2=潘多拉,3=1v1,4=glowfun,5=poyo'
     */
    @Excel(name = "平台", readConverterExp = "1=pd,2=潘多拉,3=1v1,4=glowfun,5=poyo")
    private Long appProject;

    @Excel(name = "客户ID")
    private Long customerId;

    @Excel(name = "昵称")
    private String customerName;

    @Excel(name = "推广")
    private String extendName;

    @Excel(name = "投手")
    private String pitcherName;

    @Excel(name = "VIP客服")
    private String vipName;

    @Excel(name = "金额（元）")
    private BigDecimal orderMoney;

    @Excel(name = "支付时间")
    private LocalDateTime orderTime;

    @Excel(name = "订单状态", readConverterExp = "1=已完成,2=已退款,3=特殊退款,4=更换投手")
    private Integer orderStatus;

    @Excel(name = "订单号")
    private String pdOrderNo;
    
    @Excel(name = "支付方式", readConverterExp = "2=Visa-2,9=手动充值,10=谷歌支付,11=苹果支付,12=Payermax,13=MyCard,14=croPay,15=wechatHK,16=paypal支付,17=富責東支付,18=uniPay支付,19=官网paypal支付,20=coinPay支付,21=代购申请,22=空中云汇,23=华为内购,24=三星内购,25=FunPay,26=蚂蚁国际")
    private Integer paymentType;
}
