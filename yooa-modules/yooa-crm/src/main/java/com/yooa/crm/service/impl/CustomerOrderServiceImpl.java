package com.yooa.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.*;
import com.yooa.crm.api.domain.dto.OrderUpMessageDto;
import com.yooa.crm.api.domain.query.CustomerRewardQuery;
import com.yooa.crm.api.domain.query.RechargeRecordPitcherQuery;

import com.yooa.crm.api.domain.vo.*;
import com.yooa.crm.mapper.*;
import com.yooa.crm.service.CustomerOrderService;
import com.yooa.extend.api.RemoteVermicelliService;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.system.api.RemoteDeptService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.AbstractMap;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单 - 服务实现层
 */
@AllArgsConstructor
@Service
public class CustomerOrderServiceImpl extends ServiceImpl<CrmCustomerOrderMapper, CrmCustomerOrder> implements CustomerOrderService {

    private final RemoteUserService remoteUserService;
    private final RemoteDeptService remoteDeptService;
    private final RemoteVermicelliService remoteVermicelliService;
    private final CrmCustomerFriendMapper crmCustomerFriendMapper;
    private final CrmFriendMapper crmFriendMapper;
    private final CrmCustomerMapper customerMapper;
    private final CrmCustomerJoinServeMapper joinServeMapper;

    private final CrmAnchorMapper anchorMapper;

    @Override
    public OrderUpMessageDto selRechargeRecord(List<Long> customerIds, Long userId) {
        SysUserVo userVo = remoteUserService.getUserList(UserQuery.builder()
                .userId(userId).build(), SecurityConstants.INNER).getData().get(0);
        List<OrderUpMessageDto> dtoList = baseMapper.getOrderUpMessage(customerIds, LocalDate.now(), userVo.getPdUserId());

        if (CollUtil.isEmpty(dtoList) || CollUtil.isEmpty(userVo.getPdUserId())) {
            return OrderUpMessageDto.builder().build();
        } else {
            OrderUpMessageDto dto = new OrderUpMessageDto();
            dtoList.forEach(d -> {
                dto.setMonthUp(d.getMonthUp().add(dto.getMonthUp()));
                dto.setMonthUpNumber(d.getMonthUpNumber() + dto.getMonthUpNumber());
                dto.setTotalUp(d.getTotalUp().add(dto.getTotalUp()));
                dto.setTotalUpNumber(d.getTotalUpNumber() + dto.getTotalUpNumber());
            });
            List<CrmCustomerOrder> orderList = baseMapper.selectList(new LambdaQueryWrapper<CrmCustomerOrder>()
                    .in(CrmCustomerOrder::getCustomerId, customerIds)
                    .in(CrmCustomerOrder::getPyExtendId, userVo.getPdUserId())
                    .orderByDesc(CrmCustomerOrder::getOrderTime));

            dto.setOrderList(orderList);
            return dto;
        }
    }

    @Override
    public ManufactureUp manufactureUp(List<Long> extendIds, List<Long> customerIds, List<CrmCustomerOrder> customerOrderList) {
        // 去查这些客户的订单
        List<CrmCustomerOrder> orderList = baseMapper.selectList(new LambdaQueryWrapper<CrmCustomerOrder>()
                .ge(CrmCustomerOrder::getCompleteTime, LocalDate.now().withDayOfMonth(1).atStartOfDay())
                .in(CrmCustomerOrder::getCustomerId, customerIds)
                .in(CrmCustomerOrder::getPyExtendId, extendIds));

        if (CollUtil.isNotEmpty(customerOrderList)) {
            orderList.addAll(customerOrderList);
        }

        // 判断规则(昨天到今天24小时内的充值数)
        BigDecimal oneDayUp = orderList.stream().filter(o -> o.getOrderTime().compareTo(LocalDateTime.now().minusDays(1)) >= 0)
                .map(CrmCustomerOrder::getOrderMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);      // 24小时充值数

        BigDecimal monthUp = orderList.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

        ManufactureUp up = ManufactureUp.builder()
                .oneDayUp(oneDayUp)
                .monthUp(monthUp)
                .build();

        return up;
    }

    @Override
    public List<CustomerOrderVo> getOrderListByCompleteDate(LocalDate completeDate) {
        return baseMapper.selectOrderListByCompleteDate(completeDate);
    }

    @Override
    public List<CrmCustomerOrder> selOrderDayMoney(List<Long> ids, String beginTime, String endTime, int type) {
        List<CrmCustomerOrder> orderList = CollUtil.newArrayList();     // 返回对象
        if (type == 0) {
            orderList = baseMapper.selExtendOrderDayMoney(ids, LocalDateTimeUtil.parse(beginTime), LocalDateTimeUtil.parse(endTime));
        } else if (type == 1) {
            orderList = baseMapper.selVipOrderDayMoney(ids, LocalDateTimeUtil.parse(beginTime), LocalDateTimeUtil.parse(endTime));
        }
        return orderList;
    }

    @Override
    public List<CrmCustomerOrder> selOrderDayNewMoney(List<Long> ids, String beginTime, String endTime, int type) {
        List<CrmCustomerOrder> orderList = CollUtil.newArrayList();     // 返回对象
        if (type == 0) {
            orderList = baseMapper.selExtendOrderDayNewMoney(ids, LocalDateTimeUtil.parse(beginTime), LocalDateTimeUtil.parse(endTime));
        } else if (type == 1) {
            orderList = baseMapper.selVipOrderDayNewMoney(ids, LocalDateTimeUtil.parse(beginTime), LocalDateTimeUtil.parse(endTime));
        }
        return orderList;
    }

    @Override
    public CrmCustomerOrder getCrmCustomerOrderByOrderId(String orderId) {
        return getOne(new LambdaQueryWrapper<CrmCustomerOrder>()
                .eq(CrmCustomerOrder::getOrderId, orderId));
    }

    @Override
    public CustomerRewardVo getRewardRecord(Page<CustomerRewardVo> page, CustomerRewardQuery customerRewardQuery) {
        // 根据好友id获取客户id
        List<Long> customerIdList = crmFriendMapper.getCustomerIdList(customerRewardQuery.getFriendId());
        if (CollectionUtil.isEmpty(customerIdList)) {
            throw new ServiceException(StrUtil.format("未查询到friendId:[{}] 在好友绑定表中存在记录", customerRewardQuery.getFriendId()));
        }
        CustomerRewardVo customerRewardVo = new CustomerRewardVo();
        //客户打赏基础数据
        List<CustomerRewardInfoVo> customerReward = baseMapper.getCustomerReward(page, customerRewardQuery, customerIdList);
        //客户打赏金额汇总
        CustomerRewardCollectVo customerRewardCollectVo = baseMapper.getCustomerRewardCollect(customerIdList);
        customerRewardVo.setCustomerRewardInfoVoList(customerReward);
        customerRewardVo.setCustomerRewardCollectVo(customerRewardCollectVo);
        customerRewardVo.setTotal(page.getTotal());
        return customerRewardVo;
    }

//    @Override
//    @DataScope(deptAlias = "d", userAlias = "u")
//    public CustomerRewardVo getRewardRecordList(Page<CustomerRewardVo> page, CustomerRewardQuery customerRewardQuery) {
//        CustomerRewardVo customerRewardVo = new CustomerRewardVo();
//        List<CustomerRewardInfoVo> customerReward = baseMapper.getRewardRecordList(page, customerRewardQuery);
//        customerRewardVo.setCustomerRewardInfoVoList(customerReward);
//        customerRewardVo.setTotal(page.getTotal());
//        return customerRewardVo;
//    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public CustomerRewardVo getRewardRecordList(Page<CustomerRewardVo> page, CustomerRewardQuery query) {
        // 预先根据名字搜索 减少联表查询
        if (StringUtils.isNotBlank(query.getQueryId())) {
            List<Long> customerIdList = customerMapper.getCustomerIdsByName(query.getQueryId());
            List<Long> anchorIdList = anchorMapper.getAnchorIdsByName(query.getQueryId());
            query.setCustomerIdList(customerIdList);
            query.setAnchorIdList(anchorIdList);
        }

        // 只查询id
        List<Long> rewardIdList = baseMapper.getPagedRewardIds(page, query);
        if (CollectionUtils.isEmpty(rewardIdList)) {
            return new CustomerRewardVo();
        }

        // 根据id查询详情
        List<CustomerRewardInfoVo> result = baseMapper.getRewardDetailByIds(rewardIdList);

        CustomerRewardVo vo = new CustomerRewardVo();
        vo.setCustomerRewardInfoVoList(result);
        vo.setTotal(page.getTotal());
        return vo;
    }


    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<RechargeRecordPitcherVo> selRechargeRecordPitcher(Page page, RechargeRecordPitcherQuery query) {
        // 处理推广部门查询：如果传入推广部门ID，需要查询该部门及其所有下级部门
        if (query.getExtendDeptId() != null) {
            List<Long> extendDeptIds = getDeptAndChildrenIds(query.getExtendDeptId());
            query.setExtendDeptIds(extendDeptIds);
        }

        // 处理投手部门查询：如果传入投手部门ID，需要查询该部门及其所有下级部门
        if (query.getPitcherDeptId() != null) {
            List<Long> pitcherDeptIds = getDeptAndChildrenIds(query.getPitcherDeptId());
            query.setPitcherDeptIds(pitcherDeptIds);
        }

        return baseMapper.selRechargeRecordPitcher(page,query);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public Map<String, Object> selRechargeRecordPitcherStatistics(RechargeRecordPitcherQuery query) {
        // 处理推广部门查询：如果传入推广部门ID，需要查询该部门及其所有下级部门
        if (query.getExtendDeptId() != null) {
            List<Long> extendDeptIds = getDeptAndChildrenIds(query.getExtendDeptId());
            query.setExtendDeptIds(extendDeptIds);
        }

        // 处理投手部门查询：如果传入投手部门ID，需要查询该部门及其所有下级部门
        if (query.getPitcherDeptId() != null) {
            List<Long> pitcherDeptIds = getDeptAndChildrenIds(query.getPitcherDeptId());
            query.setPitcherDeptIds(pitcherDeptIds);
        }

        Map<String, Object> result = baseMapper.selRechargeRecordPitcherStatistics(query);
        return result;
    }

    @Override
    @DataScope(deptAlias = "opd", userAlias = "opu")
    public List<RechargeRecordPitcherVo> selRechargeRecordPitcherForOperate(Page page, RechargeRecordPitcherQuery query) {
        // 处理推广部门查询：如果传入推广部门ID，需要查询该部门及其所有下级部门
        if (query.getExtendDeptId() != null) {
            List<Long> extendDeptIds = getDeptAndChildrenIds(query.getExtendDeptId());
            query.setExtendDeptIds(extendDeptIds);
        }

        // 处理投手部门查询：如果传入投手部门ID，需要查询该部门及其所有下级部门
        if (query.getPitcherDeptId() != null) {
            List<Long> pitcherDeptIds = getDeptAndChildrenIds(query.getPitcherDeptId());
            query.setPitcherDeptIds(pitcherDeptIds);
        }
        // 处理运营部门查询：如果传入运营部门ID，需要查询该部门及其所有下级部门
        if (query.getOperateDeptId() != null) {
            List<Long> operateDeptIds = getDeptAndChildrenIds(query.getOperateDeptId());
            query.setOperateDeptIds(operateDeptIds);
        }

        return baseMapper.selRechargeRecordPitcherForOperate(page, query);
    }

    @Override
    @DataScope(deptAlias = "opd", userAlias = "opu")
    public Map<String, Object> selRechargeRecordPitcherStatisticsForOperate(RechargeRecordPitcherQuery query) {
        // 处理推广部门查询：如果传入推广部门ID，需要查询该部门及其所有下级部门
        if (query.getExtendDeptId() != null) {
            List<Long> extendDeptIds = getDeptAndChildrenIds(query.getExtendDeptId());
            query.setExtendDeptIds(extendDeptIds);
        }

        // 处理投手部门查询：如果传入投手部门ID，需要查询该部门及其所有下级部门
        if (query.getPitcherDeptId() != null) {
            List<Long> pitcherDeptIds = getDeptAndChildrenIds(query.getPitcherDeptId());
            query.setPitcherDeptIds(pitcherDeptIds);
        }
        // 处理运营部门查询：如果传入运营部门ID，需要查询该部门及其所有下级部门
        if (query.getOperateDeptId() != null) {
            List<Long> operateDeptIds = getDeptAndChildrenIds(query.getOperateDeptId());
            query.setOperateDeptIds(operateDeptIds);
        }

        Map<String, Object> result = baseMapper.selRechargeRecordPitcherStatisticsForOperate(query);
        return result;
    }

    /**
     * 获取部门及其所有下级部门的ID列表
     * @param deptId 部门ID
     * @return 部门ID列表（包含自身和所有下级部门）
     */
    private List<Long> getDeptAndChildrenIds(Long deptId) {
        try {
            // 查询该部门及其所有下级部门
            DeptQuery deptQuery = DeptQuery.builder()
                    .ancestors(deptId)
                    .build();
            List<SysDept> childrenDepts = remoteDeptService.getDeptList(deptQuery, SecurityConstants.INNER).getData();

            // 提取部门ID列表
            List<Long> deptIds = childrenDepts.stream()
                    .map(SysDept::getDeptId)
                    .collect(Collectors.toList());

            // 添加当前部门ID（如果不在列表中）
            if (!deptIds.contains(deptId)) {
                deptIds.add(deptId);
            }

            return deptIds;
        } catch (Exception e) {
            // 如果查询失败，返回原部门ID
            return List.of(deptId);
        }
    }

    /**
     * 内部方法(刷二交的结束时间)
     */
    private void refreshServeLoseTime() {
        List<CrmCustomerJoinServe> joinServeList = joinServeMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinServe>()
                .eq(CrmCustomerJoinServe::getStatus, 1)
                .apply("lose_time IS NULL")
                .groupBy(CrmCustomerJoinServe::getCustomerId)
                .having("COUNT(customer_id) > 1"));

        List<Long> customerIds = joinServeList.stream().map(CrmCustomerJoinServe::getCustomerId).toList();

        customerIds.forEach(c -> {
            List<CrmCustomerJoinServe> js = joinServeMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinServe>()
                    .eq(CrmCustomerJoinServe::getCustomerId, c)
                    .eq(CrmCustomerJoinServe::getStatus, 1));
            if (CollUtil.isNotEmpty(js) && js.size() > 1) {
                js = js.stream().sorted(Comparator.comparing(CrmCustomerJoinServe::getJoinTime)).collect(Collectors.toList());

                for (int i = 0; i < js.size() - 1; i++) {
                    js.get(i).setLoseTime(js.get(i + 1).getJoinTime());
                    joinServeMapper.updateById(js.get(i));
                }
            }
        });
    }


    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerRewardInfoVo> getRewardRecordExportList(Page page, CustomerRewardQuery query) {
        // 校验时间 只能查三个月类的数据
        TimeCheck(query);
        return baseMapper.getRewardRecordExportInfoList(query);
    }


    public void TimeCheck(CustomerRewardQuery query) {
        if(StrUtil.isBlank(query.getQueryId())
                && (query.getStartTime() == null && query.getEndTime() == null)){
            throw new ServiceException("导出接口必须筛选时间条件或id条件");
        }
        if(query.getStartTime() != null && query.getEndTime() != null){
            // 假设是接口传进来的
            LocalDateTime startTime = query.getStartTime();
            LocalDateTime endTime = query.getEndTime();

            // 判断是否是今年
//            int currentYear = Year.now().getValue();
//            boolean isThisYear = startTime.getYear() == currentYear && endTime.getYear() == currentYear;

            // 判断时间间隔是否 <= 3个月
            long monthsBetween = ChronoUnit.MONTHS.between(startTime, endTime);
            boolean isLessThan3Months = monthsBetween <= 3;

//            if (!isThisYear) {
//                throw new ServiceException("时间必须是今年！");
//            }
            if (!isLessThan3Months) {
                throw new ServiceException("时间间隔不能超过三个月！");
            }
        }

    }

    /**
     * 内部方法(根据订单去刷粉丝登记(注:只能补在好友和客户在绑定中的、解绑的不能补))
     */
    private void onOrderUpRefreshVermicelli() {
        List<CrmCustomerOrder> orderList = baseMapper.selectList(new LambdaQueryWrapper<CrmCustomerOrder>()
                .ge(CrmCustomerOrder::getOrderTime, "2024-01-01 00:00:00"));

        if (CollUtil.isNotEmpty(orderList)) {
            // 根据客户ID分组
            List<Long> customerIds = orderList.stream().map(CrmCustomerOrder::getCustomerId).distinct().toList();

            // 筛查出有关联好友的客户集
            List<CrmCustomerFriend> friendCustomerIds = crmCustomerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                    .in(CrmCustomerFriend::getCustomerId, customerIds)
                    .eq(CrmCustomerFriend::getStatus, 0)
                    .apply("end_time IS NULL"));
            customerIds = friendCustomerIds.stream().map(CrmCustomerFriend::getCustomerId).distinct().collect(Collectors.toList());

            if (CollUtil.isNotEmpty(customerIds)) {
                List<CrmCustomerFriend> customerFriends = crmCustomerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                        .in(CrmCustomerFriend::getFriendId, friendCustomerIds.stream().map(CrmCustomerFriend::getFriendId).distinct().toList())
                        .eq(CrmCustomerFriend::getStatus, 0)
                        .apply("end_time IS NULL"));

                for (Long customerId : customerIds) {
                    // 客户的好友
                    Long friendId = friendCustomerIds.stream().filter(f -> f.getCustomerId().equals(customerId)).findFirst().get().getFriendId();
                    CrmFriend friend = crmFriendMapper.selectById(friendId);

                    // 好友领取人
                    SysUserVo userVo = remoteUserService.getUserById(friend.getExtendId(), SecurityConstants.INNER).getData();
                    if (ObjUtil.isNull(userVo)) {
                        continue;
                    }

                    // 好友下的客户集
                    List<Long> juniorCustomerIds = customerFriends.stream().filter(c -> c.getFriendId().equals(friendId)).map(CrmCustomerFriend::getCustomerId).distinct().toList();

                    // 好友下的客户订单集
                    List<CrmCustomerOrder> juniorOrderList = orderList.stream().filter(o ->
                            juniorCustomerIds.contains(o.getCustomerId())
                                    && userVo.getPdUserId().contains(o.getPyExtendId())
                    ).collect(Collectors.toList());
                    // 订单排序
                    juniorOrderList = juniorOrderList.stream().sorted(Comparator.comparing(CrmCustomerOrder::getOrderTime)).collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(juniorOrderList)) {

                        // 客户的客服
                        SysUserVo serveUserVo = new SysUserVo();
                        List<CrmCustomer> customerList = customerMapper.selectList(new LambdaQueryWrapper<CrmCustomer>()
                                .in(CrmCustomer::getCustomerId, juniorCustomerIds));
                        List<Long> serveIds = customerList.stream().filter(c -> ObjUtil.isNotNull(c.getServeId())).map(CrmCustomer::getServeId).distinct().collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(serveIds)) {
                            List<SysUserVo> userVoList = remoteUserService.getUserList(UserQuery.builder().pdUserId(serveIds).build(), SecurityConstants.INNER).getData();
                            if (CollUtil.isNotEmpty(userVoList)) {
                                serveUserVo = userVoList.get(0);
                            }
                        }

                        // 粉丝登记模版
                        ExtendVermicelli vermicelli = new ExtendVermicelli();
                        vermicelli.setFriendId(friendId);
                        vermicelli.setCustomerIds(juniorCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                        vermicelli.setExtendId(userVo.getUserId());
                        vermicelli.setExtendDeptId(userVo.getDeptId());
                        vermicelli.setServeId(serveUserVo.getUserId());
                        vermicelli.setServeDeptId(serveUserVo.getDeptId());
                        vermicelli.setCreateTime(LocalDateTime.now());
                        vermicelli.setRemark("根据订单去刷粉丝登记2");
                        vermicelli.setCreateBy(1L);

                        // 好友已录入的粉丝登记
                        List<ExtendVermicelli> extendVermicelliList = remoteVermicelliService.selExtendVermicelliList(
                                CollUtil.newArrayList(userVo.getUserId()),
                                null,
                                CollUtil.newArrayList(friendId),
                                null,
                                null,
                                SecurityConstants.INNER).getData();

                        boolean blsc = extendVermicelliList.stream().count() == 0;
                        boolean bl200 = extendVermicelliList.stream().filter(e -> e.getFansType() == 0).count() == 0;
                        boolean bl500 = extendVermicelliList.stream().filter(e -> e.getFansType() == 1).count() == 0;

                        if (blsc) {
                            vermicelli.setFansType(5);
                            vermicelli.setRecordDate(juniorOrderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).get().toLocalDate());
                            remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                        }

                        if (bl200 || bl500) {
                            // 按天分组并累加 orderMoney
                            Map<LocalDate, BigDecimal> dailyOrderMoneyMap = juniorOrderList.stream()
                                    .collect(Collectors.groupingBy(
                                            order -> order.getOrderTime().toLocalDate(), // 按天分组
                                            Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                    ));
                            if (bl200) {
                                LocalDate firstGreaterThan200 = dailyOrderMoneyMap.entrySet().stream()
                                        .sorted(Map.Entry.comparingByKey())         // 排序
                                        .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) > 0)
                                        .map(Map.Entry::getKey)
                                        .findFirst()
                                        .orElse(null);

                                if (ObjUtil.isNotNull(firstGreaterThan200)) {
                                    vermicelli.setFansType(0);
                                    vermicelli.setRecordDate(firstGreaterThan200);
                                    remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                                }
                            }
                            if (bl500) {
                                LocalDate firstGreaterThan500 = dailyOrderMoneyMap.entrySet().stream()
                                        .sorted(Map.Entry.comparingByKey())         // 排序
                                        .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) > 0)
                                        .map(Map.Entry::getKey)
                                        .findFirst()
                                        .orElse(null);

                                if (ObjUtil.isNotNull(firstGreaterThan500)) {
                                    vermicelli.setFansType(1);
                                    vermicelli.setRecordDate(firstGreaterThan500);
                                    remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                                }
                            }
                        }

                        // 根据月份分组
                        List<Integer> months = juniorOrderList.stream()
                                .map(order -> order.getOrderTime())
                                .map(orderTime -> {
                                    int month = orderTime.getMonthValue();
                                    return month;
                                }).distinct().collect(Collectors.toList());

                        for (Integer m : months) {
                            boolean bl5k = extendVermicelliList.stream().filter(e -> e.getFansType() == 2
                                    && e.getRecordDate().getMonthValue() == m).count() == 0;
                            boolean bl5w = extendVermicelliList.stream().filter(e -> e.getFansType() == 3
                                    && e.getRecordDate().getMonthValue() == m).count() == 0;

                            if (bl5k || bl5w) {
                                boolean bl5kOlb = extendVermicelliList.stream().filter(v -> v.getFansType() == 2).count() == 0;
                                boolean bl5wOlb = extendVermicelliList.stream().filter(v -> v.getFansType() == 3).count() == 0;

                                BigDecimal monthUp = juniorOrderList.stream().filter(o -> o.getOrderTime().getMonthValue() == m)
                                        .map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

                                if (bl5k && monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0) {
                                    vermicelli.setFansType(2);
                                    vermicelli.setStatus(bl5kOlb ? "0" : "1");
                                    vermicelli.setRecordDate(juniorOrderList.stream()
                                            .filter(o -> o.getOrderTime().getMonthValue() == m)
                                            .reduce(
                                                    new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                    (acc, order) -> {
                                                        BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                        if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                            return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                        } else {
                                                            return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                        }
                                                    },
                                                    (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                            ).getValue().toLocalDate());
                                    remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                                }
                                if (bl5w && monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0) {
                                    vermicelli.setFansType(3);
                                    vermicelli.setStatus(bl5wOlb ? "0" : "1");
                                    vermicelli.setRecordDate(juniorOrderList.stream()
                                            .filter(o -> o.getOrderTime().getMonthValue() == m)
                                            .reduce(
                                                    new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                    (acc, order) -> {
                                                        BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                        if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {   // 统计第一次累加到5万时的订单时间
                                                            return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                        } else {
                                                            return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                        }
                                                    },
                                                    (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                            ).getValue().toLocalDate());
                                    remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "exu")
    public List<RechargeRecordPitcherVo> selRechargeRecordExtend(Page page, RechargeRecordPitcherQuery query) {
        // 处理推广部门查询：如果传入推广部门ID，需要查询该部门及其所有下级部门
        if (query.getExtendDeptId() != null) {
            List<Long> extendDeptIds = getDeptAndChildrenIds(query.getExtendDeptId());
            query.setExtendDeptIds(extendDeptIds);
        }

        // 处理投手部门查询：如果传入投手部门ID，需要查询该部门及其所有下级部门
        if (query.getPitcherDeptId() != null) {
            List<Long> pitcherDeptIds = getDeptAndChildrenIds(query.getPitcherDeptId());
            query.setPitcherDeptIds(pitcherDeptIds);
        }

        return baseMapper.selRechargeRecordExtend(page, query);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "exu")
    public Map<String, Object> selRechargeRecordExtendStatistics(RechargeRecordPitcherQuery query) {
        // 处理推广部门查询：如果传入推广部门ID，需要查询该部门及其所有下级部门
        if (query.getExtendDeptId() != null) {
            List<Long> extendDeptIds = getDeptAndChildrenIds(query.getExtendDeptId());
            query.setExtendDeptIds(extendDeptIds);
        }

        // 处理投手部门查询：如果传入投手部门ID，需要查询该部门及其所有下级部门
        if (query.getPitcherDeptId() != null) {
            List<Long> pitcherDeptIds = getDeptAndChildrenIds(query.getPitcherDeptId());
            query.setPitcherDeptIds(pitcherDeptIds);
        }

        Map<String, Object> result = baseMapper.selRechargeRecordExtendStatistics(query);
        return result;
    }
}
