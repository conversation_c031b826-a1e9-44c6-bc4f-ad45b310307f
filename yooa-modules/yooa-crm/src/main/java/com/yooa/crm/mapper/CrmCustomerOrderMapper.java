package com.yooa.crm.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.dto.OrderUpMessageDto;
import com.yooa.crm.api.domain.query.CustomerRewardQuery;
import com.yooa.crm.api.domain.query.RechargeRecordPitcherQuery;

import com.yooa.crm.api.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 订单 - 数据层
 */
public interface CrmCustomerOrderMapper extends BaseMapper<CrmCustomerOrder> {

    /**
     * 查询用户充值信息(月充值、总充值、月充值笔数、总充值笔数)
     *
     * @param ids  客户ID集
     * @param time 时间只能算这个时间月份的充值
     * @return 总充值、月充值、总充值笔数、月充值笔数
     */
    List<OrderUpMessageDto> getOrderUpMessage(@Param("ids") List<Long> ids,
                                              @Param("time") LocalDate time,
                                              @Param("pdIds") List<Long> pdIds);

    /**
     * 查询用户订单数据
     */
    List<CustomerOrderVo> selectOrderListByCompleteDate(@Param("completeDate") LocalDate completeDate);

    /**
     * 查询推广订单的总金额(按天分组)
     *
     * @param ids       要查询的id集(为空查所有)
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    List<CrmCustomerOrder> selExtendOrderDayMoney(@Param("ids") List<Long> ids,
                                                  @Param("beginTime") LocalDateTime beginTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查询vip订单的总金额(按天分组)
     *
     * @param ids       要查询的id集(为空查所有)
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    List<CrmCustomerOrder> selVipOrderDayMoney(@Param("ids") List<Long> ids,
                                               @Param("beginTime") LocalDateTime beginTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询推广订单的新增金额(按天分组)
     *
     * @param ids       要查询的id集(为空查所有)
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    List<CrmCustomerOrder> selExtendOrderDayNewMoney(@Param("ids") List<Long> ids,
                                                     @Param("beginTime") LocalDateTime beginTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询vip订单的新增金额(按天分组)
     *
     * @param ids       要查询的id集(为空查所有)
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    List<CrmCustomerOrder> selVipOrderDayNewMoney(@Param("ids") List<Long> ids,
                                                  @Param("beginTime") LocalDateTime beginTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查询充值退款信息
     *
     * @param customerIds 用户id
     * @return
     */
    List<CustomerRefundRecordVo> getChargeRefundData(@Param("customerIds") List<Long> customerIds);

    /**
     * 查询客户打赏基础信息
     */
    List<CustomerRewardInfoVo> getCustomerReward(Page<CustomerRewardVo> page, @Param("customerRewardQuery") CustomerRewardQuery customerRewardQuery, @Param("customerIdList") List<Long> customerIdList);

    /**
     * 查询客户打赏汇总
     */
    CustomerRewardCollectVo getCustomerRewardCollect(@Param("customerId") List<Long> customerId);

    CustomerRewardCollectVo getRewardCollect();

    /**
     *
     * 客户列表层级打赏记录
     */
    List<CustomerRewardInfoVo> getRewardRecordList(Page<CustomerRewardVo> page, @Param("query") CustomerRewardQuery customerRewardQuery);

    /**
     * 集算此推广下客户集的总充值
     *
     * @param customerIds 客户ID集
     * @param extendIds   推广ID集
     * @return
     */
    BigDecimal calculationExtendTotalMoney(@Param("customerIds") List<Long> customerIds,
                                           @Param("extendIds") List<Long> extendIds);

    /**
     * 集算此客服下客户集的总充值
     *
     * @param customerIds 客户ID集
     * @param vipIds      客服ID集
     * @return
     */
    BigDecimal calculationVipTotalMoney(@Param("customerIds") List<Long> customerIds,
                                        @Param("vipIds") List<Long> vipIds);

    Integer getRewardRecordCount(@Param("query") CustomerRewardQuery customerRewardQuery);

    List<Long> getPagedRewardIds(Page page,@Param("query") CustomerRewardQuery query);

    List<CustomerRewardInfoVo> getRewardDetailByIds(@Param("ids") List<Long> rewardIdList);

    List<CustomerRewardInfoVo> getRewardRecordExportInfoList(@Param("query") CustomerRewardQuery query);



    List<RechargeRecordPitcherVo> selRechargeRecordPitcher(Page page,@Param("query") RechargeRecordPitcherQuery query);

    Map<String, Object> selRechargeRecordPitcherStatistics(@Param("query") RechargeRecordPitcherQuery query);

    /**
     * 运营 - 充值记录
     */
    List<RechargeRecordPitcherVo> selRechargeRecordPitcherForOperate(Page page, @Param("query") RechargeRecordPitcherQuery query);

    /**
     * 运营 - 充值记录 - 统计
     */
    Map<String, Object> selRechargeRecordPitcherStatisticsForOperate(@Param("query") RechargeRecordPitcherQuery query);

    /**
     * 推广 - 充值记录
     */
    List<RechargeRecordPitcherVo> selRechargeRecordExtend(Page page, @Param("query") RechargeRecordPitcherQuery query);

    /**
     * 推广 - 充值记录 - 统计
     */
    Map<String, Object> selRechargeRecordExtendStatistics(@Param("query") RechargeRecordPitcherQuery query);
}





